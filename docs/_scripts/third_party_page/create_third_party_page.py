#!/usr/bin/env python
"""Create the third party page for the documentation."""

import argparse
from typing import List
from typing import TypedDict

import yaml

MARKDOWN = """\
[//]: # (This file is automatically generated using a script in docs/_scripts. Do not edit this file directly!)
# Community Agents

If you’re looking for other prebuilt libraries, explore the community-built options 
below. These libraries can extend LangGraph's functionality in various ways.

## 📚 Available Libraries
[//]: # (This file is automatically generated using a script in docs/_scripts. Do not edit this file directly!)

:::python
{python_library_list}

## ✨ Contributing Your Library

Have you built an awesome open-source library using LangGraph? We'd love to feature 
your project on the official LangGraph documentation pages! 🏆

To share your project, simply open a Pull Request adding an entry for your package in our [packages.yml]({langgraph_url}) file.

**Guidelines**

- Your repo must be distributed as an installable package on PyPI 📦
- The repo should either use the Graph API (exposing a `StateGraph` instance) or 
  the Functional API (exposing an `entrypoint`).
- The package must include documentation (e.g., a `README.md` or docs site) 
  explaining how to use it.

We'll review your contribution and merge it in!

Thanks for contributing! 🚀
:::

:::js
{js_library_list}

## ✨ Contributing Your Library

Have you built an awesome open-source library using LangGraph? We'd love to feature 
your project on the official LangGraph documentation pages! 🏆

To share your project, simply open a Pull Request adding an entry for your package in our [packages.yml]({langgraph_url}) file.

**Guidelines**

- Your repo must be distributed as an installable package on npm 📦
- The repo should either use the Graph API (exposing a `StateGraph` instance) or 
  the Functional API (exposing an `entrypoint`).
- The package must include documentation (e.g., a `README.md` or docs site) 
  explaining how to use it.

We'll review your contribution and merge it in!

Thanks for contributing! 🚀
:::
"""


class ResolvedPackage(TypedDict):
    name: str
    """The name of the package."""
    repo: str
    """Repository ID within github. Format is: [orgname]/[repo_name]."""
    monorepo_path: str | None
    """Optional: The path to the package in the monorepo. Must be relative to the root of the monorepo."""
    language: str
    """The language of the package. (either 'python' or 'js')"""
    weekly_downloads: int | None
    """The weekly download count of the package."""
    description: str
    """A brief description of what the package does."""

def generate_package_table(resolved_packages: List[ResolvedPackage]) -> str:
    """Generate the package table for the third party page.
    """
    sorted_packages = sorted(
        resolved_packages, key=lambda p: p["weekly_downloads"] or 0, reverse=True
    )
    rows = [
        "| Name | GitHub URL | Description | Weekly Downloads | Stars |",
        "| --- | --- | --- | --- | --- |",
    ]
    for package in sorted_packages:
        name = f"**{package['name']}**"
        
        monorepo_path = package.get("monorepo_path", "")
        if monorepo_path:
            monorepo_path = monorepo_path[1:] if monorepo_path.startswith('/') else monorepo_path
            repo_url_suffix = f"/tree/main/{monorepo_path}"
        else:
            repo_url_suffix = ""
        repo_url = f"https://github.com/{package['repo']}{repo_url_suffix}"

        stars_badge = (
            f"https://img.shields.io/github/stars/{package['repo']}?style=social"
        )
        stars = f"![GitHub stars]({stars_badge})"
        downloads = package["weekly_downloads"] or "-"
        row = f"| {name} | {repo_url} | {package['description']} | {downloads} | {stars}"
        rows.append(row)
    return "\n".join(rows)

def generate_markdown(resolved_packages: List[ResolvedPackage]) -> str:
    """Generate the markdown content for the third party page.

    Args:
        resolved_packages: A list of resolved package information.

    Returns:
        The markdown content as a string.
    """
    # Update the URL to the actual file once the initial version is merged
    langgraph_url = (
        "https://github.com/langchain-ai/langgraph/blob/main/docs"
        "/_scripts/third_party_page/packages.yml"
    )

    python_library_list = generate_package_table(
        [p for p in resolved_packages if p["language"] == "python"]
    )
    js_library_list = generate_package_table(
        [p for p in resolved_packages if p["language"] == "js"]
    )
    
    markdown_content = MARKDOWN.format(
        python_library_list=python_library_list,
        js_library_list=js_library_list,
        langgraph_url=langgraph_url,
    )
    return markdown_content


def main(input_file: str, output_file: str) -> None:
    """Main function to create the third party page.

    Args:
        input_file: Path to the input YAML file containing resolved package information.
        output_file: Path to the output file for the third party page.
        language: The language for which to generate the third party page.
    """
    # Parse the input YAML file
    with open(input_file, "r") as f:
        resolved_packages: List[ResolvedPackage] = yaml.safe_load(f)

    markdown_content = generate_markdown(resolved_packages)

    # Write the markdown content to the output file
    with open(output_file, "w", encoding="utf-8") as f:
        f.write(markdown_content)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Create the third party page.")
    parser.add_argument(
        "input_file",
        help="Path to the input YAML file containing resolved package information.",
    )
    parser.add_argument(
        "output_file", help="Path to the output file for the third party page."
    )
    args = parser.parse_args()

    main(args.input_file, args.output_file)
