{"cells": [{"attachments": {}, "cell_type": "markdown", "id": "a2b182eb-1e31-43c8-85b1-706508dfa370", "metadata": {}, "source": ["# How to add multi-turn conversation in a multi-agent application (functional API)\n", "\n", "!!! info \"Prerequisites\"\n", "    This guide assumes familiarity with the following:\n", "\n", "    - [Multi-agent systems](../../concepts/multi_agent)\n", "    - [Human-in-the-loop](../../concepts/human_in_the_loop)\n", "    - [Functional API](../../concepts/functional_api)\n", "    - [Command](../../concepts/low_level/#command)\n", "    - [LangGraph Glossary](../../concepts/low_level/)\n", "\n", "\n", "In this how-to guide, we’ll build an application that allows an end-user to engage in a *multi-turn conversation* with one or more agents. We'll create a node that uses an [`interrupt`](../../reference/types/#langgraph.types.interrupt) to collect user input and routes back to the **active** agent.\n", "\n", "The agents will be implemented as tasks in a workflow that executes agent steps and determines the next action:\n", "\n", "1. **Wait for user input** to continue the conversation, or\n", "2. **Route to another agent** (or back to itself, such as in a loop) via a [**handoff**](../../concepts/multi_agent/#handoffs).\n", "\n", "```python\n", "from langgraph.func import entrypoint, task\n", "from langgraph.prebuilt import create_react_agent\n", "from langchain_core.tools import tool\n", "from langgraph.types import interrupt\n", "\n", "\n", "# Define a tool to signal intent to hand off to a different agent\n", "# Note: this is not using Command(goto) syntax for navigating to different agents:\n", "# `workflow()` below handles the handoffs explicitly\n", "@tool(return_direct=True)\n", "def transfer_to_hotel_advisor():\n", "    \"\"\"Ask hotel advisor agent for help.\"\"\"\n", "    return \"Successfully transferred to hotel advisor\"\n", "\n", "\n", "# define an agent\n", "travel_advisor_tools = [transfer_to_hotel_advisor, ...]\n", "travel_advisor = create_react_agent(model, travel_advisor_tools)\n", "\n", "\n", "# define a task that calls an agent\n", "@task\n", "def call_travel_advisor(messages):\n", "    response = travel_advisor.invoke({\"messages\": messages})\n", "    return response[\"messages\"]\n", "\n", "\n", "# define the multi-agent network workflow\n", "@entrypoint(checkpointer)\n", "def workflow(messages):\n", "    call_active_agent = call_travel_advisor\n", "    while True:\n", "        agent_messages = call_active_agent(messages).result()\n", "        ai_msg = get_last_ai_msg(agent_messages)\n", "        if not ai_msg.tool_calls:\n", "            user_input = interrupt(value=\"Ready for user input.\")\n", "            messages = messages + [{\"role\": \"user\", \"content\": user_input}]\n", "            continue\n", "\n", "        messages = messages + agent_messages\n", "        call_active_agent = get_next_agent(messages)\n", "    return entrypoint.final(value=agent_messages[-1], save=messages)\n", "```"]}, {"cell_type": "markdown", "id": "faaa4444-cd06-4813-b9ca-c9700fe12cb7", "metadata": {}, "source": ["## Setup\n", "\n", "First, let's install the required packages"]}, {"cell_type": "code", "execution_count": 1, "id": "05038da0-31df-4066-a1a4-c4ccb5db4d3a", "metadata": {}, "outputs": [], "source": ["# %%capture --no-stderr\n", "# %pip install -U langgraph langchain-anthropic"]}, {"cell_type": "code", "execution_count": 2, "id": "0bcff5d4-130e-426d-9285-40d0f72c7cd3", "metadata": {}, "outputs": [{"name": "stdin", "output_type": "stream", "text": ["ANTHROPIC_API_KEY:  ········\n"]}], "source": ["import getpass\n", "import os\n", "\n", "\n", "def _set_env(var: str):\n", "    if not os.environ.get(var):\n", "        os.environ[var] = getpass.getpass(f\"{var}: \")\n", "\n", "\n", "_set_env(\"ANTHROPIC_API_KEY\")"]}, {"cell_type": "markdown", "id": "c3ec6e48-85dc-4905-ba50-985e5d4788e6", "metadata": {}, "source": ["<div class=\"admonition tip\">\n", "    <p class=\"admonition-title\">Set up <a href=\"https://smith.langchain.com\">LangSmith</a> for LangGraph development</p>\n", "    <p style=\"padding-top: 5px;\">\n", "        Sign up for LangSmith to quickly spot issues and improve the performance of your LangGraph projects. LangSmith lets you use trace data to debug, test, and monitor your LLM apps built with LangGraph — read more about how to get started <a href=\"https://docs.smith.langchain.com\">here</a>. \n", "    </p>\n", "</div>"]}, {"attachments": {}, "cell_type": "markdown", "id": "c217c3fe-ca50-45a1-be91-912bc83ed8b3", "metadata": {}, "source": ["In this example we will build a team of travel assistant agents that can communicate with each other.\n", "\n", "We will create 2 agents:\n", "\n", "* `travel_advisor`: can help with travel destination recommendations. Can ask `hotel_advisor` for help.\n", "* `hotel_advisor`: can help with hotel recommendations. Can ask `travel_advisor` for help.\n", "\n", "This is a fully-connected network - every agent can talk to any other agent. "]}, {"cell_type": "code", "execution_count": 3, "id": "eb51463a-4425-44ad-91d5-f21fd5b4e3b3", "metadata": {}, "outputs": [], "source": ["import random\n", "from typing_extensions import Literal\n", "from langchain_core.tools import tool\n", "\n", "\n", "@tool\n", "def get_travel_recommendations():\n", "    \"\"\"Get recommendation for travel destinations\"\"\"\n", "    return random.choice([\"aruba\", \"turks and caicos\"])\n", "\n", "\n", "@tool\n", "def get_hotel_recommendations(location: Literal[\"aruba\", \"turks and caicos\"]):\n", "    \"\"\"Get hotel recommendations for a given destination.\"\"\"\n", "    return {\n", "        \"aruba\": [\n", "            \"The Ritz-Carlton, Aruba (Palm Beach)\"\n", "            \"Bucuti & Tara Beach Resort (Eagle Beach)\"\n", "        ],\n", "        \"turks and caicos\": [\"Grace Bay Club\", \"COMO Parrot Cay\"],\n", "    }[location]\n", "\n", "\n", "@tool(return_direct=True)\n", "def transfer_to_hotel_advisor():\n", "    \"\"\"Ask hotel advisor agent for help.\"\"\"\n", "    return \"Successfully transferred to hotel advisor\"\n", "\n", "\n", "@tool(return_direct=True)\n", "def transfer_to_travel_advisor():\n", "    \"\"\"Ask travel advisor agent for help.\"\"\"\n", "    return \"Successfully transferred to travel advisor\""]}, {"cell_type": "markdown", "id": "7f5b2a7f", "metadata": {}, "source": ["!!! note \"Transfer tools\"\n", "\n", "    You might have noticed that we're using `@tool(return_direct=True)` in the transfer tools. This is done so that individual agents (e.g., `travel_advisor`) can exit the ReAct loop early once these tools are called. This is the desired behavior, as we want to detect when the agent calls this tool and hand control off _immediately_ to a different agent. \n", "    \n", "    **NOTE**: This is meant to work with the prebuilt [`create_react_agent`][langgraph.prebuilt.chat_agent_executor.create_react_agent] -- if you are building a custom agent, make sure to manually add logic for handling early exit for tools that are marked with `return_direct`."]}, {"cell_type": "markdown", "id": "213d661e-6ba4-42b9-bc7f-6c8c423e3419", "metadata": {}, "source": ["Let's now create our agents using the prebuilt [`create_react_agent`][langgraph.prebuilt.chat_agent_executor.create_react_agent] and our multi-agent workflow. Note that will be calling [`interrupt`][langgraph.types.interrupt] every time after we get the final response from each of the agents."]}, {"cell_type": "code", "execution_count": 6, "id": "aa4bdbff-9461-46cc-aee9-8a22d3c3d9ec", "metadata": {}, "outputs": [], "source": ["import uuid\n", "\n", "from langchain_core.messages import AIMessage\n", "from langchain_anthropic import ChatAnthropic\n", "from langgraph.prebuilt import create_react_agent\n", "from langgraph.graph import add_messages\n", "from langgraph.func import entrypoint, task\n", "from langgraph.checkpoint.memory import InMemorySaver\n", "from langgraph.types import interrupt, Command\n", "\n", "model = ChatAnthropic(model=\"claude-3-5-sonnet-latest\")\n", "\n", "# Define travel advisor ReAct agent\n", "travel_advisor_tools = [\n", "    get_travel_recommendations,\n", "    transfer_to_hotel_advisor,\n", "]\n", "travel_advisor = create_react_agent(\n", "    model,\n", "    travel_advisor_tools,\n", "    prompt=(\n", "        \"You are a general travel expert that can recommend travel destinations (e.g. countries, cities, etc). \"\n", "        \"If you need hotel recommendations, ask 'hotel_advisor' for help. \"\n", "        \"You MUST include human-readable response before transferring to another agent.\"\n", "    ),\n", ")\n", "\n", "\n", "@task\n", "def call_travel_advisor(messages):\n", "    # You can also add additional logic like changing the input to the agent / output from the agent, etc.\n", "    # NOTE: we're invoking the ReAct agent with the full history of messages in the state\n", "    response = travel_advisor.invoke({\"messages\": messages})\n", "    return response[\"messages\"]\n", "\n", "\n", "# Define hotel advisor ReAct agent\n", "hotel_advisor_tools = [get_hotel_recommendations, transfer_to_travel_advisor]\n", "hotel_advisor = create_react_agent(\n", "    model,\n", "    hotel_advisor_tools,\n", "    prompt=(\n", "        \"You are a hotel expert that can provide hotel recommendations for a given destination. \"\n", "        \"If you need help picking travel destinations, ask 'travel_advisor' for help.\"\n", "        \"You MUST include human-readable response before transferring to another agent.\"\n", "    ),\n", ")\n", "\n", "\n", "@task\n", "def call_hotel_advisor(messages):\n", "    response = hotel_advisor.invoke({\"messages\": messages})\n", "    return response[\"messages\"]\n", "\n", "\n", "checkpointer = InMemorySaver()\n", "\n", "\n", "def string_to_uuid(input_string):\n", "    return str(uuid.uuid5(uuid.NAMESPACE_URL, input_string))\n", "\n", "\n", "@entrypoint(checkpointer=checkpointer)\n", "def multi_turn_graph(messages, previous):\n", "    previous = previous or []\n", "    messages = add_messages(previous, messages)\n", "    call_active_agent = call_travel_advisor\n", "    while True:\n", "        agent_messages = call_active_agent(messages).result()\n", "        messages = add_messages(messages, agent_messages)\n", "        # Find the last AI message\n", "        # If one of the handoff tools is called, the last message returned\n", "        # by the agent will be a ToolMessage because we set them to have\n", "        # \"return_direct=True\". This means that the last AIMessage will\n", "        # have tool calls.\n", "        # Otherwise, the last returned message will be an AIMessage with\n", "        # no tool calls, which means we are ready for new input.\n", "        ai_msg = next(m for m in reversed(agent_messages) if isinstance(m, AIMessage))\n", "        if not ai_msg.tool_calls:\n", "            user_input = interrupt(value=\"Ready for user input.\")\n", "            # Add user input as a human message\n", "            # NOTE: we generate unique ID for the human message based on its content\n", "            # it's important, since on subsequent invocations previous user input (interrupt) values\n", "            # will be looked up again and we will attempt to add them again here\n", "            # `add_messages` deduplicates messages based on the ID, ensuring correct message history\n", "            human_message = {\n", "                \"role\": \"user\",\n", "                \"content\": user_input,\n", "                \"id\": string_to_uuid(user_input),\n", "            }\n", "            messages = add_messages(messages, [human_message])\n", "            continue\n", "\n", "        tool_call = ai_msg.tool_calls[-1]\n", "        if tool_call[\"name\"] == \"transfer_to_hotel_advisor\":\n", "            call_active_agent = call_hotel_advisor\n", "        elif tool_call[\"name\"] == \"transfer_to_travel_advisor\":\n", "            call_active_agent = call_travel_advisor\n", "        else:\n", "            raise ValueError(f\"Expected transfer tool, got '{tool_call['name']}'\")\n", "\n", "    return entrypoint.final(value=agent_messages[-1], save=messages)"]}, {"cell_type": "markdown", "id": "af856e1b-41fc-4041-8cbf-3818a60088e0", "metadata": {}, "source": ["## Test multi-turn conversation\n", "\n", "Let's test a multi turn conversation with this application."]}, {"cell_type": "code", "execution_count": 7, "id": "2b6fde57-86e3-440e-a7bf-f1e9b5ed9ff2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- Conversation Turn 1 ---\n", "\n", "User: {'role': 'user', 'content': 'i wanna go somewhere warm in the caribbean', 'id': 'f48d82a7-7efa-43f5-ad4c-541758c95f61'}\n", "\n", "call_travel_advisor: Based on the recommendations, Aruba would be an excellent choice for your Caribbean getaway! Known as \"One Happy Island,\" Aruba offers:\n", "- Year-round warm weather with consistent temperatures around 82°F (28°C)\n", "- Beautiful white sand beaches like Eagle Beach and Palm Beach\n", "- Crystal clear waters perfect for swimming and snorkeling\n", "- Minimal rainfall and location outside the hurricane belt\n", "- Rich culture blending Dutch and Caribbean influences\n", "- Various activities from water sports to desert-like landscape exploration\n", "- Excellent dining and shopping options\n", "\n", "Would you like me to help you find suitable accommodations in Aruba? I can transfer you to our hotel advisor who can recommend specific hotels based on your preferences.\n", "\n", "--- Conversation Turn 2 ---\n", "\n", "User: Command(resume='could you recommend a nice hotel in one of the areas and tell me which area it is.')\n", "\n", "call_hotel_advisor: I can recommend two excellent options in different areas:\n", "\n", "1. The Ritz-Carlton, Aruba - Located in Palm Beach\n", "- Luxury beachfront resort\n", "- Located in the vibrant Palm Beach area, known for its lively atmosphere\n", "- Close to restaurants, shopping, and nightlife\n", "- Perfect for those who want a more active vacation with plenty of amenities nearby\n", "\n", "2. Bucuti & Tara Beach Resort - Located in Eagle Beach\n", "- Adults-only boutique resort\n", "- Situated on the quieter Eagle Beach\n", "- Known for its romantic atmosphere and excellent service\n", "- Ideal for couples seeking a more peaceful, intimate setting\n", "\n", "Would you like more specific information about either of these properties or their locations?\n", "\n", "--- Conversation Turn 3 ---\n", "\n", "User: Command(resume='i like the first one. could you recommend something to do near the hotel?')\n", "\n", "call_travel_advisor: Near The Ritz-Carlton in Palm Beach, here are some popular activities you can enjoy:\n", "\n", "1. Palm Beach Strip - Take a walk along this bustling strip filled with restaurants, shops, and bars\n", "2. Visit the Bubali Bird Sanctuary - Just a short distance away\n", "3. Try your luck at the Stellaris Casino - Located right in The Ritz-Carlton\n", "4. Water Sports at Palm Beach - Right in front of the hotel you can:\n", "   - Go parasailing\n", "   - Try jet skiing\n", "   - Take a sunset sailing cruise\n", "5. Visit the Palm Beach Plaza Mall - High-end shopping just a short walk away\n", "6. Enjoy dinner at Madame <PERSON>'s - One of Aruba's most famous restaurants nearby\n", "\n", "Would you like more specific information about any of these activities or other suggestions in the area?\n"]}], "source": ["thread_config = {\"configurable\": {\"thread_id\": uuid.uuid4()}}\n", "\n", "inputs = [\n", "    # 1st round of conversation,\n", "    {\n", "        \"role\": \"user\",\n", "        \"content\": \"i wanna go somewhere warm in the caribbean\",\n", "        \"id\": str(uuid.uuid4()),\n", "    },\n", "    # Since we're using `interrupt`, we'll need to resume using the Command primitive.\n", "    # 2nd round of conversation,\n", "    Command(\n", "        resume=\"could you recommend a nice hotel in one of the areas and tell me which area it is.\"\n", "    ),\n", "    # 3rd round of conversation,\n", "    Command(\n", "        resume=\"i like the first one. could you recommend something to do near the hotel?\"\n", "    ),\n", "]\n", "\n", "for idx, user_input in enumerate(inputs):\n", "    print()\n", "    print(f\"--- Conversation Turn {idx + 1} ---\")\n", "    print()\n", "    print(f\"User: {user_input}\")\n", "    print()\n", "    for update in multi_turn_graph.stream(\n", "        user_input,\n", "        config=thread_config,\n", "        stream_mode=\"updates\",\n", "    ):\n", "        for node_id, value in update.items():\n", "            if isinstance(value, list) and value:\n", "                last_message = value[-1]\n", "                if isinstance(last_message, dict) or last_message.type != \"ai\":\n", "                    continue\n", "                print(f\"{node_id}: {last_message.content}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}