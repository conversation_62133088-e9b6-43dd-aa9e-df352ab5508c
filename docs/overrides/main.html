{% extends "base.html" %}

{% block analytics %}
<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-T35S4S46');</script>
<!-- End Google Tag Manager -->
{% endblock %}


{% block extrahead %}
  <meta name="algolia-site-verification" content="165B7E7C89E49946" />
  <script>
    // Simple copy page functionality - uses original markdown source
    function copyPageAsMarkdown() {
        const markdownScript = document.getElementById('page-markdown-content');
        if (!markdownScript) {
            alert('Markdown content not available for this page');
            return;
        }
        
        try {
            let rawContent = markdownScript.textContent;
            
            // Safe HTML entity decoding function
            function decodeHtmlEntities(text) {
                const parser = new DOMParser();
                const doc = parser.parseFromString(text, 'text/html');
                return doc.documentElement.textContent || '';
            }
            
            // Always decode HTML entities since the browser might encode them
            rawContent = decodeHtmlEntities(rawContent);
            
            
            const data = JSON.parse(rawContent);
            const content = `Source: ${window.location.href}\n\n${data.markdown}`;
            
            navigator.clipboard.writeText(content).then(() => {
                // Simple notification
                const notification = document.createElement('div');
                notification.textContent = 'Page content copied to clipboard';
                notification.style.cssText = 'position:fixed;top:20px;right:20px;background:#4CAF50;color:white;padding:10px 16px;border-radius:4px;z-index:9999;box-shadow:0 2px 10px rgba(0,0,0,0.2);';
                document.body.appendChild(notification);
                setTimeout(() => notification.remove(), 3000);
            }).catch(() => {
                alert('Failed to copy content');
            });
        } catch (e) {
            console.error('Failed to parse page content:', e);
            alert('Failed to parse page content: ' + e.message);
        }
    }

    // Add dropdown button to header when page loads
    document.addEventListener('DOMContentLoaded', function() {
        const headerSource = document.querySelector('.md-header__source');
        if (headerSource) {
            // Create dropdown container
            const dropdownContainer = document.createElement('div');
            dropdownContainer.style.cssText = 'position:relative;display:inline-block;margin-left:8px;';
            
            // Create main button
            const button = document.createElement('button');
            button.innerHTML = 'Copy page <span style="margin-left:8px;font-size:12px;color:#9ca3af;">▾</span>';
            button.style.cssText = 'background:transparent;border:1px solid #d1d5db;padding:6px 12px;border-radius:4px;cursor:pointer;font-size:14px;color:#374151;transition:all 0.2s ease;white-space:nowrap;display:flex;align-items:center;';
            
            // Create dropdown menu
            const dropdown = document.createElement('div');
            dropdown.className = 'copy-page-dropdown';
            dropdown.style.cssText = 'position:absolute;top:100%;left:0;background:white;border:1px solid #e5e7eb;border-radius:6px;box-shadow:0 4px 12px rgba(0,0,0,0.15);z-index:1000;min-width:180px;display:none;padding:4px 0;';
            
            // Create dropdown options
            const option1 = document.createElement('div');
            option1.textContent = 'Copy as Markdown for LLMs';
            option1.className = 'copy-page-option';
            option1.style.cssText = 'padding:8px 16px;cursor:pointer;font-size:14px;color:#374151;margin:2px 0;';
            option1.onmouseover = function() { 
                this.style.background = document.documentElement.getAttribute('data-md-color-scheme') === 'slate' ? '#4a5568' : '#f8fafc'; 
            };
            option1.onmouseout = function() { this.style.background = 'transparent'; };
            option1.onclick = function() {
                // Check if we're on a reference page
                if (window.location.pathname.includes('/reference/')) {
                    alert('Copy Page not yet available in API reference pages.');
                } else {
                    copyPageAsMarkdown();
                }
                dropdown.style.display = 'none';
            };
            
            const option2 = document.createElement('div');
            option2.textContent = "View LangGraph's llms.txt";
            option2.className = 'copy-page-option';
            option2.style.cssText = 'padding:8px 16px;cursor:pointer;font-size:14px;color:#374151;margin:2px 0;';
            option2.onmouseover = function() { 
                this.style.background = document.documentElement.getAttribute('data-md-color-scheme') === 'slate' ? '#4a5568' : '#f8fafc'; 
            };
            option2.onmouseout = function() { this.style.background = 'transparent'; };
            option2.onclick = function() {
                window.open('/langgraph/llms-txt-overview/', '_blank');
                dropdown.style.display = 'none';
            };
            
            // Add options to dropdown
            dropdown.appendChild(option1);
            dropdown.appendChild(option2);
            
            // Button hover effects
            button.onmouseover = function() { 
                this.style.background = '#f3f4f6'; 
                this.style.borderColor = '#9ca3af'; 
            };
            button.onmouseout = function() { 
                this.style.background = 'transparent'; 
                this.style.borderColor = '#d1d5db'; 
            };
            
            // Toggle dropdown
            button.onclick = function(e) {
                e.stopPropagation();
                dropdown.style.display = dropdown.style.display === 'none' ? 'block' : 'none';
            };
            
            // Close dropdown when clicking outside
            document.addEventListener('click', function() {
                dropdown.style.display = 'none';
            });
            
            // Assemble dropdown
            dropdownContainer.appendChild(button);
            dropdownContainer.appendChild(dropdown);
            headerSource.parentNode.insertBefore(dropdownContainer, headerSource.nextSibling);
        }
    });
  </script>
  <style>
    @import url("https://fonts.googleapis.com/css2?family=Public+Sans&display=swap");
    :root {
      --md-primary-fg-color: #333333;
      --md-accent-fg-color: #1E88E5;
      --md-default-bg-color: #FFFFFF;
      --md-default-fg-color: #333333;
      --md-text-font-family: "Public Sans", sans-serif;
    }

    body {
      font-family: var(--md-text-font-family);
      background-color: var(--md-default-bg-color);
      color: var(--md-default-fg-color);
    }

    .md-main {
      background-color: #FFFFFF;
    }

    .md-footer {
      background-color: #F5F5F5;
      color: #666666;
    }

    .md-footer-meta {
      background-color: #4d4d4d;
    }

    .md-typeset a {
      color: #1E88E5;
    }

    .md-typeset a:hover {
      color: #1565C0;
    }

    .md-nav__link--active,
    .md-nav__link:active {
      color: #1E88E5;
    }

    .md-search__input {
      background-color: #F5F5F5;
      color: #333333;
    }

    .md-search__input:hover,
    .md-search__input:focus {
      background-color: #EEEEEE;
    }
    /* Table of contents styles */
    .md-nav--secondary .md-nav__item--active > .md-nav__link {
      font-weight: bold;
      color: var(--md-primary-fg-color);
    }

    .md-nav--secondary .md-nav__item--nested > .md-nav__link {
      font-weight: normal;
      color: var(--md-default-fg-color);
    }

    .md-nav--secondary .md-nav__item--nested > .md-nav__link::before {
      content: "";
      display: inline-block;
      width: 6px;
      height: 6px;
      background-color: var(--md-default-fg-color);
      border-radius: 50%;
      margin-right: 0.5rem;
    }

    [data-md-color-scheme="slate"] {
      --md-default-bg-color: #1E1E1E;
      --md-default-fg-color: #FFFFFF;
      --md-accent-fg-color: #64B5F6;
    }

    [data-md-color-scheme="slate"] .md-main {
      background-color: #1E1E1E;
    }

    [data-md-color-scheme="slate"] .navbar {
      background-color: #1E1E1E;
      color: #FFFFFF;
      box-shadow: none;
    }

    [data-md-color-scheme="slate"] .md-footer {
      background-color: #1E1E1E;
      color: #BDBDBD;
    }

    [data-md-color-scheme="slate"] .md-header {
      background-color: #1E1E1E;
      color: #BDBDBD;
    }

    [data-md-color-scheme="slate"] .md-tabs {
      background-color: #1E1E1E;
      color: #BDBDBD;
    }

    [data-md-color-scheme="slate"] .md-search__input {
      background-color: #F5F5F5;
      color: #333333;
    }

    [data-md-color-scheme="slate"] .md-search__icon {
      color: #333333;
    }

    [data-md-color-scheme="slate"] .md-search__input::placeholder {
      color: #333333;
    }

    [data-md-color-scheme="slate"] .md-footer-meta {
      background-color: #4d4d4d;
    }

    [data-md-color-scheme="slate"] .md-typeset a {
      color: #64B5F6;
    }

    [data-md-color-scheme="slate"] .md-typeset a:hover {
      color: #90CAF9;
    }

    .notebook-links {
      display: flex;
      justify-content: flex-end;
      margin-bottom: 1rem;
    }
    .notebook-links .md-content__button {
      margin-left: 0.5rem;
    }

    [data-md-color-scheme=default] .logo-dark {
      display: none !important;
    }

    [data-md-color-scheme=slate] .logo-light {
      display: none !important;
    }

    .jupyter-wrapper .jp-CodeCell .jp-Cell-inputWrapper .jp-InputPrompt.jp-InputArea-prompt {
      display: none !important;
    }

    .jupyter-wrapper .jp-Notebook .jp-Cell .jp-OutputPrompt {
      display: none !important;
    }

    .md-banner {
      background-color: #CFC9FA;
      color: #000000;
    }

    .md-banner a {
      color: #000000;
      text-decoration: underline;
    }

    .md-banner a:hover {
      color: #000000;
    }

    .md-header__title {
      visibility: hidden;
    }

    /* Dark mode banner styling */
    [data-md-color-scheme="slate"] .md-banner {
      background-color: #CFC9FA; /* Same light purple for both modes */
      color: #000000;
    }

    [data-md-color-scheme="slate"] .md-banner a {
      color: #000000;
    }

    [data-md-color-scheme="slate"] .md-banner a:hover {
      color: #000000;
    }

    /* Copy page dropdown dark mode support */
    [data-md-color-scheme="slate"] .copy-page-dropdown {
      background: #1f2937 !important;
      border-color: #374151 !important;
      box-shadow: 0 4px 12px rgba(0,0,0,0.5) !important;
    }

    [data-md-color-scheme="slate"] .copy-page-option {
      color: #e5e7eb !important;
    }

  </style>
{% endblock %}

{% block content %}
<div class="notebook-links">
  {% if page.nb_url %}
    <a href="{{ page.nb_url }}" title="Download Notebook" class="md-content__button md-icon">
      {% include ".icons/material/download.svg" %}
    </a>
  {% endif %}
</div>

{{ super() }}
{% endblock content %}


{% block htmltitle %}
  {% if page.meta and page.meta.title %}
    <title>{{ page.meta.title }}</title>
  {% elif page.title and not page.is_homepage %}
    <title>{{ page.title | striptags }}</title>
  {% else %}
    <title>{{ config.site_name }}</title>
  {% endif %}
{% endblock %}

{% block announce %}
LangGraph Platform docs are moving! Find the LangGraph Platform docs at the new <a href="https://docs.langchain.com/langgraph-platform" target="_blank">LangChain Docs</a> site!
{% endblock %}
