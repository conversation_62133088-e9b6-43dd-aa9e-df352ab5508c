# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 8
  cacheKey: 10c0

"@cfworker/json-schema@npm:^4.0.2":
  version: 4.1.1
  resolution: "@cfworker/json-schema@npm:4.1.1"
  checksum: 10c0/b5253486d346b7de6feec9c73954f612b11019dacb9023d710a5666df2f5fc145dd88b6b913c88726c6d97e2e258a515fa2cab177f58b18da6bac3738cbc4739
  languageName: node
  linkType: hard

"@langchain/core@npm:^0.3.38":
  version: 0.3.38
  resolution: "@langchain/core@npm:0.3.38"
  dependencies:
    "@cfworker/json-schema": "npm:^4.0.2"
    ansi-styles: "npm:^5.0.0"
    camelcase: "npm:6"
    decamelize: "npm:1.2.0"
    js-tiktoken: "npm:^1.0.12"
    langsmith: "npm:>=0.2.8 <0.4.0"
    mustache: "npm:^4.2.0"
    p-queue: "npm:^6.6.2"
    p-retry: "npm:4"
    uuid: "npm:^10.0.0"
    zod: "npm:^3.22.4"
    zod-to-json-schema: "npm:^3.22.3"
  checksum: 10c0/3b2f042f6550cb818a33b0649110c9ef7f645b0bc23507d2d5a63b98dd4dcd28692d4a6ead70a936e4d46dd39224cc74e4bfb0389b124a183f29bbdf0b069ab0
  languageName: node
  linkType: hard

"@langchain/openai@npm:^0.4.2":
  version: 0.4.2
  resolution: "@langchain/openai@npm:0.4.2"
  dependencies:
    js-tiktoken: "npm:^1.0.12"
    openai: "npm:^4.77.0"
    zod: "npm:^3.22.4"
    zod-to-json-schema: "npm:^3.22.3"
  peerDependencies:
    "@langchain/core": ">=0.3.29 <0.4.0"
  checksum: 10c0/0a17803c9a74e3b95f77a86e45705e32f0a76fdc9bbdb6d17f64ff6fa052356bc7067f0dccb57479087f236434f4aebf6731618c616331cab3bf48bf55a376c3
  languageName: node
  linkType: hard

"@mswjs/interceptors@npm:^0.37.3":
  version: 0.37.6
  resolution: "@mswjs/interceptors@npm:0.37.6"
  dependencies:
    "@open-draft/deferred-promise": "npm:^2.2.0"
    "@open-draft/logger": "npm:^0.3.0"
    "@open-draft/until": "npm:^2.0.0"
    is-node-process: "npm:^1.2.0"
    outvariant: "npm:^1.4.3"
    strict-event-emitter: "npm:^0.5.1"
  checksum: 10c0/74f52c09c84fcbba9f1a06e462aa25b1567cf078ed27d396c76a8059c002fa9c361e711dcada0ac2aad4298f247d8e236a4fcc861c08ddf6e2ce0889368596fd
  languageName: node
  linkType: hard

"@open-draft/deferred-promise@npm:^2.2.0":
  version: 2.2.0
  resolution: "@open-draft/deferred-promise@npm:2.2.0"
  checksum: 10c0/eafc1b1d0fc8edb5e1c753c5e0f3293410b40dde2f92688211a54806d4136887051f39b98c1950370be258483deac9dfd17cf8b96557553765198ef2547e4549
  languageName: node
  linkType: hard

"@open-draft/logger@npm:^0.3.0":
  version: 0.3.0
  resolution: "@open-draft/logger@npm:0.3.0"
  dependencies:
    is-node-process: "npm:^1.2.0"
    outvariant: "npm:^1.4.0"
  checksum: 10c0/90010647b22e9693c16258f4f9adb034824d1771d3baa313057b9a37797f571181005bc50415a934eaf7c891d90ff71dcd7a9d5048b0b6bb438f31bef2c7c5c1
  languageName: node
  linkType: hard

"@open-draft/until@npm:^2.0.0":
  version: 2.1.0
  resolution: "@open-draft/until@npm:2.1.0"
  checksum: 10c0/61d3f99718dd86bb393fee2d7a785f961dcaf12f2055f0c693b27f4d0cd5f7a03d498a6d9289773b117590d794a43cd129366fd8e99222e4832f67b1653d54cf
  languageName: node
  linkType: hard

"@tsconfig/recommended@npm:^1.0.8":
  version: 1.0.8
  resolution: "@tsconfig/recommended@npm:1.0.8"
  checksum: 10c0/bd6517e3f69cf96108ab8b7d2ee70a7e64ee457bb72326524acdef6e2219813b298654e9aa57ce2f8899901c9b8fd66388b036b9ca0aa062952a83adb59bec17
  languageName: node
  linkType: hard

"@types/msgpack-lite@npm:^0.1.11":
  version: 0.1.11
  resolution: "@types/msgpack-lite@npm:0.1.11"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/d51a47a20ef5ff9b8b61d33ca3d10c992bbf10c4d4dbcbb7d1f1f9cdb2c8c1a302de36b00e1f11ef954b1bc4730add11b610996cd6ee767624b2bdd572e7b647
  languageName: node
  linkType: hard

"@types/nock@npm:^11.1.0":
  version: 11.1.0
  resolution: "@types/nock@npm:11.1.0"
  dependencies:
    nock: "npm:*"
  checksum: 10c0/d13596983b909b86c03d031220a478a4a4759a006586c02d2b6bbb7751386df04026223ccbe66289d0d4edaf5b66c7a401c62999a5e4ec3c2a242f5ec1a0433b
  languageName: node
  linkType: hard

"@types/node-fetch@npm:^2.6.4":
  version: 2.6.12
  resolution: "@types/node-fetch@npm:2.6.12"
  dependencies:
    "@types/node": "npm:*"
    form-data: "npm:^4.0.0"
  checksum: 10c0/7693acad5499b7df2d1727d46cff092a63896dc04645f36b973dd6dd754a59a7faba76fcb777bdaa35d80625c6a9dd7257cca9c401a4bab03b04480cda7fd1af
  languageName: node
  linkType: hard

"@types/node@npm:*, @types/node@npm:^22.13.1":
  version: 22.13.1
  resolution: "@types/node@npm:22.13.1"
  dependencies:
    undici-types: "npm:~6.20.0"
  checksum: 10c0/d4e56d41d8bd53de93da2651c0a0234e330bd7b1b6d071b1a94bd3b5ee2d9f387519e739c52a15c1faa4fb9d97e825b848421af4b2e50e6518011e7adb4a34b7
  languageName: node
  linkType: hard

"@types/node@npm:^18.11.18":
  version: 18.19.75
  resolution: "@types/node@npm:18.19.75"
  dependencies:
    undici-types: "npm:~5.26.4"
  checksum: 10c0/6a78833071d23dcd4010507d0a232da1cb6e939eb5b62023a01ab5f91eecb90223bda3e34aa536f02cd5c3bdf7962c754b7e2a051a8224aed5886788fce88fbf
  languageName: node
  linkType: hard

"@types/retry@npm:0.12.0":
  version: 0.12.0
  resolution: "@types/retry@npm:0.12.0"
  checksum: 10c0/7c5c9086369826f569b83a4683661557cab1361bac0897a1cefa1a915ff739acd10ca0d62b01071046fe3f5a3f7f2aec80785fe283b75602dc6726781ea3e328
  languageName: node
  linkType: hard

"@types/uuid@npm:^10.0.0":
  version: 10.0.0
  resolution: "@types/uuid@npm:10.0.0"
  checksum: 10c0/9a1404bf287164481cb9b97f6bb638f78f955be57c40c6513b7655160beb29df6f84c915aaf4089a1559c216557dc4d2f79b48d978742d3ae10b937420ddac60
  languageName: node
  linkType: hard

"abort-controller@npm:^3.0.0":
  version: 3.0.0
  resolution: "abort-controller@npm:3.0.0"
  dependencies:
    event-target-shim: "npm:^5.0.0"
  checksum: 10c0/90ccc50f010250152509a344eb2e71977fbf8db0ab8f1061197e3275ddf6c61a41a6edfd7b9409c664513131dd96e962065415325ef23efa5db931b382d24ca5
  languageName: node
  linkType: hard

"agentkeepalive@npm:^4.2.1":
  version: 4.6.0
  resolution: "agentkeepalive@npm:4.6.0"
  dependencies:
    humanize-ms: "npm:^1.2.1"
  checksum: 10c0/235c182432f75046835b05f239708107138a40103deee23b6a08caee5136873709155753b394ec212e49e60e94a378189562cb01347765515cff61b692c69187
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: "npm:^2.0.1"
  checksum: 10c0/895a23929da416f2bd3de7e9cb4eabd340949328ab85ddd6e484a637d8f6820d485f53933446f5291c3b760cbc488beb8e88573dd0f9c7daf83dccc8fe81b041
  languageName: node
  linkType: hard

"ansi-styles@npm:^5.0.0":
  version: 5.2.0
  resolution: "ansi-styles@npm:5.2.0"
  checksum: 10c0/9c4ca80eb3c2fb7b33841c210d2f20807f40865d27008d7c3f707b7f95cab7d67462a565e2388ac3285b71cb3d9bb2173de8da37c57692a362885ec34d6e27df
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: 10c0/d73e2ddf20c4eb9337e1b3df1a0f6159481050a5de457c55b14ea2e5cb6d90bb69e004c9af54737a5ee0917fcf2c9e25de67777bbe58261847846066ba75bc9d
  languageName: node
  linkType: hard

"base64-js@npm:^1.5.1":
  version: 1.5.1
  resolution: "base64-js@npm:1.5.1"
  checksum: 10c0/f23823513b63173a001030fae4f2dabe283b99a9d324ade3ad3d148e218134676f1ee8568c877cd79ec1c53158dcf2d2ba527a97c606618928ba99dd930102bf
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.1, call-bind-apply-helpers@npm:^1.0.2":
  version: 1.0.2
  resolution: "call-bind-apply-helpers@npm:1.0.2"
  dependencies:
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
  checksum: 10c0/47bd9901d57b857590431243fea704ff18078b16890a6b3e021e12d279bbf211d039155e27d7566b374d49ee1f8189344bac9833dec7a20cdec370506361c938
  languageName: node
  linkType: hard

"camelcase@npm:6":
  version: 6.3.0
  resolution: "camelcase@npm:6.3.0"
  checksum: 10c0/0d701658219bd3116d12da3eab31acddb3f9440790c0792e0d398f0a520a6a4058018e546862b6fba89d7ae990efaeb97da71e1913e9ebf5a8b5621a3d55c710
  languageName: node
  linkType: hard

"chalk@npm:^4.1.2":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: "npm:^4.1.0"
    supports-color: "npm:^7.1.0"
  checksum: 10c0/4a3fef5cc34975c898ffe77141450f679721df9dde00f6c304353fa9c8b571929123b26a0e4617bde5018977eb655b31970c297b91b63ee83bb82aeb04666880
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: "npm:~1.1.4"
  checksum: 10c0/37e1150172f2e311fe1b2df62c6293a342ee7380da7b9cfdba67ea539909afbd74da27033208d01d6d5cfc65ee7868a22e18d7e7648e004425441c0f8a15a7d7
  languageName: node
  linkType: hard

"color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: 10c0/a1a3f914156960902f46f7f56bc62effc6c94e84b2cae157a526b1c1f74b677a47ec602bf68a61abfa2b42d15b7c5651c6dbe72a43af720bc588dff885b10f95
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.8":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: "npm:~1.0.0"
  checksum: 10c0/0dbb829577e1b1e839fa82b40c07ffaf7de8a09b935cadd355a73652ae70a88b4320db322f6634a4ad93424292fa80973ac6480986247f1734a1137debf271d5
  languageName: node
  linkType: hard

"console-table-printer@npm:^2.12.1":
  version: 2.12.1
  resolution: "console-table-printer@npm:2.12.1"
  dependencies:
    simple-wcswidth: "npm:^1.0.1"
  checksum: 10c0/8f28e9c0ae5df77f5d60da3da002ecd95ebe1812b0b9e0a6d2795c81b5121b39774f32506bccf68830a838ca4d8fbb2ab8824e729dba2c5e30cdeb9df4dd5f2b
  languageName: node
  linkType: hard

"decamelize@npm:1.2.0":
  version: 1.2.0
  resolution: "decamelize@npm:1.2.0"
  checksum: 10c0/85c39fe8fbf0482d4a1e224ef0119db5c1897f8503bcef8b826adff7a1b11414972f6fef2d7dec2ee0b4be3863cf64ac1439137ae9e6af23a3d8dcbe26a5b4b2
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: 10c0/d758899da03392e6712f042bec80aa293bbe9e9ff1b2634baae6a360113e708b91326594c8a486d475c69d6259afb7efacdc3537bfcda1c6c648e390ce601b19
  languageName: node
  linkType: hard

"docs@workspace:.":
  version: 0.0.0-use.local
  resolution: "docs@workspace:."
  dependencies:
    "@langchain/core": "npm:^0.3.38"
    "@langchain/openai": "npm:^0.4.2"
    "@tsconfig/recommended": "npm:^1.0.8"
    "@types/msgpack-lite": "npm:^0.1.11"
    "@types/nock": "npm:^11.1.0"
    "@types/node": "npm:^22.13.1"
    he: "npm:^1.2.0"
    msgpack-lite: "npm:^0.1.26"
    nock: "npm:^14.0.1"
  languageName: unknown
  linkType: soft

"dunder-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "dunder-proto@npm:1.0.1"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.2.0"
  checksum: 10c0/199f2a0c1c16593ca0a145dbf76a962f8033ce3129f01284d48c45ed4e14fea9bbacd7b3610b6cdc33486cef20385ac054948fefc6272fcce645c09468f93031
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "es-define-property@npm:1.0.1"
  checksum: 10c0/3f54eb49c16c18707949ff25a1456728c883e81259f045003499efba399c08bad00deebf65cccde8c0e07908c1a225c9d472b7107e558f2a48e28d530e34527c
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: 10c0/0a61325670072f98d8ae3b914edab3559b6caa980f08054a3b872052640d91da01d38df55df797fcc916389d77fc92b8d5906cf028f4db46d7e3003abecbca85
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0, es-object-atoms@npm:^1.1.1":
  version: 1.1.1
  resolution: "es-object-atoms@npm:1.1.1"
  dependencies:
    es-errors: "npm:^1.3.0"
  checksum: 10c0/65364812ca4daf48eb76e2a3b7a89b3f6a2e62a1c420766ce9f692665a29d94fe41fe88b65f24106f449859549711e4b40d9fb8002d862dfd7eb1c512d10be0c
  languageName: node
  linkType: hard

"es-set-tostringtag@npm:^2.1.0":
  version: 2.1.0
  resolution: "es-set-tostringtag@npm:2.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
    has-tostringtag: "npm:^1.0.2"
    hasown: "npm:^2.0.2"
  checksum: 10c0/ef2ca9ce49afe3931cb32e35da4dcb6d86ab02592cfc2ce3e49ced199d9d0bb5085fc7e73e06312213765f5efa47cc1df553a6a5154584b21448e9fb8355b1af
  languageName: node
  linkType: hard

"event-lite@npm:^0.1.1":
  version: 0.1.3
  resolution: "event-lite@npm:0.1.3"
  checksum: 10c0/68d11a1e9001d713d673866fe07f6c310fa9054fc0a936dd5eacc37a793aa6b3331ddb1d85dbcb88ddbe6b04944566a0f1c5b515118e1ec2e640ffcb30858b3f
  languageName: node
  linkType: hard

"event-target-shim@npm:^5.0.0":
  version: 5.0.1
  resolution: "event-target-shim@npm:5.0.1"
  checksum: 10c0/0255d9f936215fd206156fd4caa9e8d35e62075d720dc7d847e89b417e5e62cf1ce6c9b4e0a1633a9256de0efefaf9f8d26924b1f3c8620cffb9db78e7d3076b
  languageName: node
  linkType: hard

"eventemitter3@npm:^4.0.4":
  version: 4.0.7
  resolution: "eventemitter3@npm:4.0.7"
  checksum: 10c0/5f6d97cbcbac47be798e6355e3a7639a84ee1f7d9b199a07017f1d2f1e2fe236004d14fa5dfaeba661f94ea57805385e326236a6debbc7145c8877fbc0297c6b
  languageName: node
  linkType: hard

"form-data-encoder@npm:1.7.2":
  version: 1.7.2
  resolution: "form-data-encoder@npm:1.7.2"
  checksum: 10c0/56553768037b6d55d9de524f97fe70555f0e415e781cb56fc457a68263de3d40fadea2304d4beef2d40b1a851269bd7854e42c362107071892cb5238debe9464
  languageName: node
  linkType: hard

"form-data@npm:^4.0.0":
  version: 4.0.4
  resolution: "form-data@npm:4.0.4"
  dependencies:
    asynckit: "npm:^0.4.0"
    combined-stream: "npm:^1.0.8"
    es-set-tostringtag: "npm:^2.1.0"
    hasown: "npm:^2.0.2"
    mime-types: "npm:^2.1.12"
  checksum: 10c0/373525a9a034b9d57073e55eab79e501a714ffac02e7a9b01be1c820780652b16e4101819785e1e18f8d98f0aee866cc654d660a435c378e16a72f2e7cac9695
  languageName: node
  linkType: hard

"formdata-node@npm:^4.3.2":
  version: 4.4.1
  resolution: "formdata-node@npm:4.4.1"
  dependencies:
    node-domexception: "npm:1.0.0"
    web-streams-polyfill: "npm:4.0.0-beta.3"
  checksum: 10c0/74151e7b228ffb33b565cec69182694ad07cc3fdd9126a8240468bb70a8ba66e97e097072b60bcb08729b24c7ce3fd3e0bd7f1f80df6f9f662b9656786e76f6a
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 10c0/d8680ee1e5fcd4c197e4ac33b2b4dce03c71f4d91717292785703db200f5c21f977c568d28061226f9b5900cbcd2c84463646134fd5337e7925e0942bc3f46d5
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.2.6":
  version: 1.3.0
  resolution: "get-intrinsic@npm:1.3.0"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.1.1"
    function-bind: "npm:^1.1.2"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    math-intrinsics: "npm:^1.1.0"
  checksum: 10c0/52c81808af9a8130f581e6a6a83e1ba4a9f703359e7a438d1369a5267a25412322f03dcbd7c549edaef0b6214a0630a28511d7df0130c93cfd380f4fa0b5b66a
  languageName: node
  linkType: hard

"get-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "get-proto@npm:1.0.1"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/9224acb44603c5526955e83510b9da41baf6ae73f7398875fba50edc5e944223a89c4a72b070fcd78beb5f7bdda58ecb6294adc28f7acfc0da05f76a2399643c
  languageName: node
  linkType: hard

"gopd@npm:^1.2.0":
  version: 1.2.0
  resolution: "gopd@npm:1.2.0"
  checksum: 10c0/50fff1e04ba2b7737c097358534eacadad1e68d24cccee3272e04e007bed008e68d2614f3987788428fd192a5ae3889d08fb2331417e4fc4a9ab366b2043cead
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 10c0/2e789c61b7888d66993e14e8331449e525ef42aac53c627cc53d1c3334e768bcb6abdc4f5f0de1478a25beec6f0bd62c7549058b7ac53e924040d4f301f02fd1
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.3, has-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "has-symbols@npm:1.1.0"
  checksum: 10c0/dde0a734b17ae51e84b10986e651c664379018d10b91b6b0e9b293eddb32f0f069688c841fb40f19e9611546130153e0a2a48fd7f512891fb000ddfa36f5a20e
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-tostringtag@npm:1.0.2"
  dependencies:
    has-symbols: "npm:^1.0.3"
  checksum: 10c0/a8b166462192bafe3d9b6e420a1d581d93dd867adb61be223a17a8d6dad147aa77a8be32c961bb2f27b3ef893cae8d36f564ab651f5e9b7938ae86f74027c48c
  languageName: node
  linkType: hard

"hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: "npm:^1.1.2"
  checksum: 10c0/3769d434703b8ac66b209a4cca0737519925bbdb61dd887f93a16372b14694c63ff4e797686d87c90f08168e81082248b9b028bad60d4da9e0d1148766f56eb9
  languageName: node
  linkType: hard

"he@npm:^1.2.0":
  version: 1.2.0
  resolution: "he@npm:1.2.0"
  bin:
    he: bin/he
  checksum: 10c0/a27d478befe3c8192f006cdd0639a66798979dfa6e2125c6ac582a19a5ebfec62ad83e8382e6036170d873f46e4536a7e795bf8b95bf7c247f4cc0825ccc8c17
  languageName: node
  linkType: hard

"humanize-ms@npm:^1.2.1":
  version: 1.2.1
  resolution: "humanize-ms@npm:1.2.1"
  dependencies:
    ms: "npm:^2.0.0"
  checksum: 10c0/f34a2c20161d02303c2807badec2f3b49cbfbbb409abd4f95a07377ae01cfe6b59e3d15ac609cffcd8f2521f0eb37b7e1091acf65da99aa2a4f1ad63c21e7e7a
  languageName: node
  linkType: hard

"ieee754@npm:^1.1.8":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: 10c0/b0782ef5e0935b9f12883a2e2aa37baa75da6e66ce6515c168697b42160807d9330de9a32ec1ed73149aea02e0d822e572bca6f1e22bdcbd2149e13b050b17bb
  languageName: node
  linkType: hard

"int64-buffer@npm:^0.1.9":
  version: 0.1.10
  resolution: "int64-buffer@npm:0.1.10"
  checksum: 10c0/22688f6d1f4db11eaacbf8e7f0b80a23690c29d023987302c367f8c071a53b84fa1cef6f8db0a347e9326f94ff76aa3529e8e9964e99d37fc675f5dcd835ee50
  languageName: node
  linkType: hard

"is-node-process@npm:^1.2.0":
  version: 1.2.0
  resolution: "is-node-process@npm:1.2.0"
  checksum: 10c0/5b24fda6776d00e42431d7bcd86bce81cb0b6cabeb944142fe7b077a54ada2e155066ad06dbe790abdb397884bdc3151e04a9707b8cd185099efbc79780573ed
  languageName: node
  linkType: hard

"isarray@npm:^1.0.0":
  version: 1.0.0
  resolution: "isarray@npm:1.0.0"
  checksum: 10c0/18b5be6669be53425f0b84098732670ed4e727e3af33bc7f948aac01782110eb9a18b3b329c5323bcdd3acdaae547ee077d3951317e7f133bff7105264b3003d
  languageName: node
  linkType: hard

"js-tiktoken@npm:^1.0.12":
  version: 1.0.18
  resolution: "js-tiktoken@npm:1.0.18"
  dependencies:
    base64-js: "npm:^1.5.1"
  checksum: 10c0/de2f82d41d49702d42bb417dfc9dc1ce3801d5f04ec0ac73a6f06db5aa3dadfcc13871b30447c28e0b80e37fe76a5052a7c195af657707e7c6753110881d2a26
  languageName: node
  linkType: hard

"json-stringify-safe@npm:^5.0.1":
  version: 5.0.1
  resolution: "json-stringify-safe@npm:5.0.1"
  checksum: 10c0/7dbf35cd0411d1d648dceb6d59ce5857ec939e52e4afc37601aa3da611f0987d5cee5b38d58329ceddf3ed48bd7215229c8d52059ab01f2444a338bf24ed0f37
  languageName: node
  linkType: hard

"langsmith@npm:>=0.2.8 <0.4.0":
  version: 0.3.7
  resolution: "langsmith@npm:0.3.7"
  dependencies:
    "@types/uuid": "npm:^10.0.0"
    chalk: "npm:^4.1.2"
    console-table-printer: "npm:^2.12.1"
    p-queue: "npm:^6.6.2"
    p-retry: "npm:4"
    semver: "npm:^7.6.3"
    uuid: "npm:^10.0.0"
  peerDependencies:
    openai: "*"
  peerDependenciesMeta:
    openai:
      optional: true
  checksum: 10c0/68ada1d5120376467bbf7edca17b0629f3d5a2588c91d2396a372b69217e3de960487f1c4109c36e38e0ee6a467d5f81e4b59d8f3312e480af5bb01007d179f3
  languageName: node
  linkType: hard

"math-intrinsics@npm:^1.1.0":
  version: 1.1.0
  resolution: "math-intrinsics@npm:1.1.0"
  checksum: 10c0/7579ff94e899e2f76ab64491d76cf606274c874d8f2af4a442c016bd85688927fcfca157ba6bf74b08e9439dc010b248ce05b96cc7c126a354c3bae7fcb48b7f
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 10c0/0557a01deebf45ac5f5777fe7740b2a5c309c6d62d40ceab4e23da9f821899ce7a900b7ac8157d4548ddbb7beffe9abc621250e6d182b0397ec7f10c7b91a5aa
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: "npm:1.52.0"
  checksum: 10c0/82fb07ec56d8ff1fc999a84f2f217aa46cb6ed1033fefaabd5785b9a974ed225c90dc72fff460259e66b95b73648596dbcc50d51ed69cdf464af2d237d3149b2
  languageName: node
  linkType: hard

"ms@npm:^2.0.0":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: 10c0/d924b57e7312b3b63ad21fc5b3dc0af5e78d61a1fc7cfb5457edaf26326bf62be5307cc87ffb6862ef1c2b33b0233cdb5d4f01c4c958cc0d660948b65a287a48
  languageName: node
  linkType: hard

"msgpack-lite@npm:^0.1.26":
  version: 0.1.26
  resolution: "msgpack-lite@npm:0.1.26"
  dependencies:
    event-lite: "npm:^0.1.1"
    ieee754: "npm:^1.1.8"
    int64-buffer: "npm:^0.1.9"
    isarray: "npm:^1.0.0"
  bin:
    msgpack: ./bin/msgpack
  checksum: 10c0/ba571dca7d789fa033523b74c1aae52bbd023834bcad3f397f481889a8df6cdb6b163b73307be8b744c420ce6d3c0e697f588bb96984c04f9dcf09370b9f12d4
  languageName: node
  linkType: hard

"mustache@npm:^4.2.0":
  version: 4.2.0
  resolution: "mustache@npm:4.2.0"
  bin:
    mustache: bin/mustache
  checksum: 10c0/1f8197e8a19e63645a786581d58c41df7853da26702dbc005193e2437c98ca49b255345c173d50c08fe4b4dbb363e53cb655ecc570791f8deb09887248dd34a2
  languageName: node
  linkType: hard

"nock@npm:*, nock@npm:^14.0.1":
  version: 14.0.1
  resolution: "nock@npm:14.0.1"
  dependencies:
    "@mswjs/interceptors": "npm:^0.37.3"
    json-stringify-safe: "npm:^5.0.1"
    propagate: "npm:^2.0.0"
  checksum: 10c0/258d123eb726f81268ee8ba2b69f8fdd5763c416027542bf5d255dae9c21ab3fcff936f2f57fa829dac4371aea2d4bd34a2dc3837008f317bb9893bf48fe736d
  languageName: node
  linkType: hard

"node-domexception@npm:1.0.0":
  version: 1.0.0
  resolution: "node-domexception@npm:1.0.0"
  checksum: 10c0/5e5d63cda29856402df9472335af4bb13875e1927ad3be861dc5ebde38917aecbf9ae337923777af52a48c426b70148815e890a5d72760f1b4d758cc671b1a2b
  languageName: node
  linkType: hard

"node-fetch@npm:^2.6.7":
  version: 2.7.0
  resolution: "node-fetch@npm:2.7.0"
  dependencies:
    whatwg-url: "npm:^5.0.0"
  peerDependencies:
    encoding: ^0.1.0
  peerDependenciesMeta:
    encoding:
      optional: true
  checksum: 10c0/b55786b6028208e6fbe594ccccc213cab67a72899c9234eb59dba51062a299ea853210fcf526998eaa2867b0963ad72338824450905679ff0fa304b8c5093ae8
  languageName: node
  linkType: hard

"openai@npm:^4.77.0":
  version: 4.83.0
  resolution: "openai@npm:4.83.0"
  dependencies:
    "@types/node": "npm:^18.11.18"
    "@types/node-fetch": "npm:^2.6.4"
    abort-controller: "npm:^3.0.0"
    agentkeepalive: "npm:^4.2.1"
    form-data-encoder: "npm:1.7.2"
    formdata-node: "npm:^4.3.2"
    node-fetch: "npm:^2.6.7"
  peerDependencies:
    ws: ^8.18.0
    zod: ^3.23.8
  peerDependenciesMeta:
    ws:
      optional: true
    zod:
      optional: true
  bin:
    openai: bin/cli
  checksum: 10c0/8ca7cf1e67a91b746402575acff035dc664f4b50f95533229caa581a9c4f16e9692765fc53be3e8b0ecda0c5efc6735e803154b02c7d69119149f622792e0bb0
  languageName: node
  linkType: hard

"outvariant@npm:^1.4.0, outvariant@npm:^1.4.3":
  version: 1.4.3
  resolution: "outvariant@npm:1.4.3"
  checksum: 10c0/5976ca7740349cb8c71bd3382e2a762b1aeca6f33dc984d9d896acdf3c61f78c3afcf1bfe9cc633a7b3c4b295ec94d292048f83ea2b2594fae4496656eba992c
  languageName: node
  linkType: hard

"p-finally@npm:^1.0.0":
  version: 1.0.0
  resolution: "p-finally@npm:1.0.0"
  checksum: 10c0/6b8552339a71fe7bd424d01d8451eea92d379a711fc62f6b2fe64cad8a472c7259a236c9a22b4733abca0b5666ad503cb497792a0478c5af31ded793d00937e7
  languageName: node
  linkType: hard

"p-queue@npm:^6.6.2":
  version: 6.6.2
  resolution: "p-queue@npm:6.6.2"
  dependencies:
    eventemitter3: "npm:^4.0.4"
    p-timeout: "npm:^3.2.0"
  checksum: 10c0/5739ecf5806bbeadf8e463793d5e3004d08bb3f6177bd1a44a005da8fd81bb90f80e4633e1fb6f1dfd35ee663a5c0229abe26aebb36f547ad5a858347c7b0d3e
  languageName: node
  linkType: hard

"p-retry@npm:4":
  version: 4.6.2
  resolution: "p-retry@npm:4.6.2"
  dependencies:
    "@types/retry": "npm:0.12.0"
    retry: "npm:^0.13.1"
  checksum: 10c0/d58512f120f1590cfedb4c2e0c42cb3fa66f3cea8a4646632fcb834c56055bb7a6f138aa57b20cc236fb207c9d694e362e0b5c2b14d9b062f67e8925580c73b0
  languageName: node
  linkType: hard

"p-timeout@npm:^3.2.0":
  version: 3.2.0
  resolution: "p-timeout@npm:3.2.0"
  dependencies:
    p-finally: "npm:^1.0.0"
  checksum: 10c0/524b393711a6ba8e1d48137c5924749f29c93d70b671e6db761afa784726572ca06149c715632da8f70c090073afb2af1c05730303f915604fd38ee207b70a61
  languageName: node
  linkType: hard

"propagate@npm:^2.0.0":
  version: 2.0.1
  resolution: "propagate@npm:2.0.1"
  checksum: 10c0/01e1023b60ae4050d1a2783f976d7db702022dbdb70dba797cceedad8cfc01b3939c41e77032f8c32aa9d93192fe937ebba1345e8604e5ce61fd3b62ee3003b8
  languageName: node
  linkType: hard

"retry@npm:^0.13.1":
  version: 0.13.1
  resolution: "retry@npm:0.13.1"
  checksum: 10c0/9ae822ee19db2163497e074ea919780b1efa00431d197c7afdb950e42bf109196774b92a49fc9821f0b8b328a98eea6017410bfc5e8a0fc19c85c6d11adb3772
  languageName: node
  linkType: hard

"semver@npm:^7.6.3":
  version: 7.7.1
  resolution: "semver@npm:7.7.1"
  bin:
    semver: bin/semver.js
  checksum: 10c0/fd603a6fb9c399c6054015433051bdbe7b99a940a8fb44b85c2b524c4004b023d7928d47cb22154f8d054ea7ee8597f586605e05b52047f048278e4ac56ae958
  languageName: node
  linkType: hard

"simple-wcswidth@npm:^1.0.1":
  version: 1.0.1
  resolution: "simple-wcswidth@npm:1.0.1"
  checksum: 10c0/2befead4c97134424aa3fba593a81daa9934fd61b9e4c65374b57ac5eecc2f2be1984b017bbdbc919923e19b77f2fcbdb94434789b9643fa8c3fde3a2a6a4b6f
  languageName: node
  linkType: hard

"strict-event-emitter@npm:^0.5.1":
  version: 0.5.1
  resolution: "strict-event-emitter@npm:0.5.1"
  checksum: 10c0/f5228a6e6b6393c57f52f62e673cfe3be3294b35d6f7842fc24b172ae0a6e6c209fa83241d0e433fc267c503bc2f4ffdbe41a9990ff8ffd5ac425ec0489417f7
  languageName: node
  linkType: hard

"supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10c0/afb4c88521b8b136b5f5f95160c98dee7243dc79d5432db7efc27efb219385bbc7d9427398e43dd6cc730a0f87d5085ce1652af7efbe391327bc0a7d0f7fc124
  languageName: node
  linkType: hard

"tr46@npm:~0.0.3":
  version: 0.0.3
  resolution: "tr46@npm:0.0.3"
  checksum: 10c0/047cb209a6b60c742f05c9d3ace8fa510bff609995c129a37ace03476a9b12db4dbf975e74600830ef0796e18882b2381fb5fb1f6b4f96b832c374de3ab91a11
  languageName: node
  linkType: hard

"undici-types@npm:~5.26.4":
  version: 5.26.5
  resolution: "undici-types@npm:5.26.5"
  checksum: 10c0/bb673d7876c2d411b6eb6c560e0c571eef4a01c1c19925175d16e3a30c4c428181fb8d7ae802a261f283e4166a0ac435e2f505743aa9e45d893f9a3df017b501
  languageName: node
  linkType: hard

"undici-types@npm:~6.20.0":
  version: 6.20.0
  resolution: "undici-types@npm:6.20.0"
  checksum: 10c0/68e659a98898d6a836a9a59e6adf14a5d799707f5ea629433e025ac90d239f75e408e2e5ff086afc3cace26f8b26ee52155293564593fbb4a2f666af57fc59bf
  languageName: node
  linkType: hard

"uuid@npm:^10.0.0":
  version: 10.0.0
  resolution: "uuid@npm:10.0.0"
  bin:
    uuid: dist/bin/uuid
  checksum: 10c0/eab18c27fe4ab9fb9709a5d5f40119b45f2ec8314f8d4cf12ce27e4c6f4ffa4a6321dc7db6c515068fa373c075b49691ba969f0010bf37f44c37ca40cd6bf7fe
  languageName: node
  linkType: hard

"web-streams-polyfill@npm:4.0.0-beta.3":
  version: 4.0.0-beta.3
  resolution: "web-streams-polyfill@npm:4.0.0-beta.3"
  checksum: 10c0/a9596779db2766990117ed3a158e0b0e9f69b887a6d6ba0779940259e95f99dc3922e534acc3e5a117b5f5905300f527d6fbf8a9f0957faf1d8e585ce3452e8e
  languageName: node
  linkType: hard

"webidl-conversions@npm:^3.0.0":
  version: 3.0.1
  resolution: "webidl-conversions@npm:3.0.1"
  checksum: 10c0/5612d5f3e54760a797052eb4927f0ddc01383550f542ccd33d5238cfd65aeed392a45ad38364970d0a0f4fea32e1f4d231b3d8dac4a3bdd385e5cf802ae097db
  languageName: node
  linkType: hard

"whatwg-url@npm:^5.0.0":
  version: 5.0.0
  resolution: "whatwg-url@npm:5.0.0"
  dependencies:
    tr46: "npm:~0.0.3"
    webidl-conversions: "npm:^3.0.0"
  checksum: 10c0/1588bed84d10b72d5eec1d0faa0722ba1962f1821e7539c535558fb5398d223b0c50d8acab950b8c488b4ba69043fd833cc2697056b167d8ad46fac3995a55d5
  languageName: node
  linkType: hard

"zod-to-json-schema@npm:^3.22.3":
  version: 3.24.1
  resolution: "zod-to-json-schema@npm:3.24.1"
  peerDependencies:
    zod: ^3.24.1
  checksum: 10c0/dd4e72085003e41a3f532bd00061f27041418a4eb176aa6ce33042db08d141bd37707017ee9117d97738ae3f22fc3e1404ea44e6354634ac5da79d7d3173b4ee
  languageName: node
  linkType: hard

"zod@npm:^3.22.4":
  version: 3.24.1
  resolution: "zod@npm:3.24.1"
  checksum: 10c0/0223d21dbaa15d8928fe0da3b54696391d8e3e1e2d0283a1a070b5980a1dbba945ce631c2d1eccc088fdbad0f2dfa40155590bf83732d3ac4fcca2cc9237591b
  languageName: node
  linkType: hard
